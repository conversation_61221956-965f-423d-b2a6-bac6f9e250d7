"""Main RAG chain orchestration for retrieval, generation, and execution in the
TerraBLOOM RAG System.
"""

import time
from typing import Optional

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import Run<PERSON><PERSON><PERSON><PERSON>b<PERSON>, RunnablePassthrough
from loguru import logger

from src.models import Conversation, Message
from src.utils.query_validator import QueryValidator
from src.utils.response_processor import ResponseProcessor
from src.utils.metrics import RAGMetrics

# LangSmith Tracing (optional, for debugging and monitoring)
# Remove or refactor the usage of 'config' at the top-level scope.
# Move any config-dependent logic (like <PERSON><PERSON><PERSON> tracing) into the ServiceContainer's initialize method, where config is available.


# Refactor RAGChain to accept dependencies via constructor
class RAGChain:
    """Main RAG chain that orchestrates retrieval, generation, and execution."""

    def __init__(self, llm_manager, db_manager, conversation_store, retriever):
        """Initialize the RAGChain with its required dependencies.

        Args:
            llm_manager: The LLMManager instance for language model operations.
            db_manager: The DatabaseManager instance for database access.
            conversation_store: The ConversationStore instance for managing conversations.
            retriever: The IVFFAISSRetriever instance for schema/document retrieval.
        """
        self.llm_manager = llm_manager
        self.db_manager = db_manager
        self.conversation_store = conversation_store
        self.retriever = retriever
        self.query_validator = QueryValidator()
        self.response_processor = ResponseProcessor()
        self.metrics = RAGMetrics()
        self.chat_history: list[Message] = []
        self.active_conversation_id = None
        self._setup_chain()

    def _setup_chain(self):
        """Setup the RAG chain."""
        self.chain = RunnablePassthrough().assign(
            schema=lambda x: self._get_schema_context(x)
        ).assign(query=lambda x: self._generate_query_and_store(x)).assign(
            result=lambda x: self._safe_execute_query(x["query"])
        ) | RunnableLambda(
            lambda x: self._format_response(x)
        )

    def _get_schema_context(self, inputs):
        """Get relevant schema context from vector store."""
        question = inputs["question"] if isinstance(inputs, dict) else inputs
        documents = self.retriever.invoke(question)
        return "\n".join([doc.page_content for doc in documents])

    def _generate_query_and_store(self, inputs):
        """Generate SQL query using LLM and store in chat history."""
        logger.info(
            f"Schema length: {len(inputs['schema']) if isinstance(inputs['schema'], str) else 'Not a string'}"
        )

        # Validate inputs
        if not inputs.get("question"):
            raise ValueError("Question cannot be empty or None")

        if not inputs["schema"]:
            logger.warning(
                "Empty schema context! This will likely result in poor query generation."
            )
            logger.warning(f"Question was: {inputs['question']}")

        # Generate the query
        logger.info(f"Generating SQL query for question: {inputs['question']}")
        query = self.llm_manager.generate_sql_query(
            question=inputs["question"],
            schema=inputs["schema"] or "",  # Ensure schema is never None
            chat_history=inputs.get("chat_history", []),
        )
        # We'll let the ask_question method add the AI response to history
        # after getting the final formatted result
        self.chat_history.append(AIMessage(content=query))
        return query

    def _generate_query(self, inputs):
        """Generate SQL query using LLM (original method kept for reference)."""
        logger.info(
            f"Schema length: {len(inputs['schema']) if isinstance(inputs['schema'], str) else 'Not a string'}"
        )

        if not inputs["schema"]:
            logger.warning(
                "Empty schema context! This will likely result in poor query generation."
            )
            logger.warning(f"Question was: {inputs['question']}")

        return self.llm_manager.generate_sql_query(
            question=inputs["question"],
            schema=inputs["schema"],
            chat_history=inputs.get("chat_history", []),
        )

    def _safe_execute_query(self, query: str) -> str:
        """Execute SQL query safely."""
        try:
            logger.info(f"Executing query: {query}")
            return self.db_manager.execute_query(query)
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            return f"Error executing query: {str(e)}"

    def _format_response(self, inputs):
        """Format the final response."""
        return self.llm_manager.format_response(
            question=inputs["question"],
            query=inputs["query"],
            result=inputs["result"],
            schema=inputs["schema"],
        )

    def ask_question(self, question: str) -> str:
        """Ask a question and get a response with comprehensive metrics tracking."""
        start_time = time.time()
        component_times = {}
        success = False

        try:
            # Ensure we have an active conversation
            if not self.active_conversation_id:
                self.active_conversation_id = (
                    self.conversation_store.create_conversation("New Conversation")
                )

            # Validate the question input
            if (
                question is None
                or not isinstance(question, str)
                or question.strip() == ""
            ):
                raise ValueError("Question cannot be empty or None")

            # Add user question to chat history first
            self.chat_history.append(HumanMessage(content=question))

            # Create a list of messages for the last 4 exchanges (8 messages or fewer)
            recent_history = (
                self.chat_history[-8:]
                if len(self.chat_history) > 8
                else self.chat_history
            )

            # Track retrieval performance
            retrieval_start = time.time()

            # Get response from chain with enhanced tracking
            chain_inputs = {"question": question, "chat_history": recent_history}
            response = self.chain.invoke(chain_inputs)

            component_times['total_chain'] = time.time() - retrieval_start

            # Extract response content
            response_content = (
                response if isinstance(response, str) else str(response)
            )

            # Track response quality
            response_start = time.time()

            # Get processed response data if available from chain inputs
            processed_response = chain_inputs.get('processed_response')

            self.metrics.track_response_quality(
                question=question,
                final_response=response_content,
                processing_time=time.time() - response_start,
                processed_response=processed_response
            )

            component_times['response_processing'] = time.time() - response_start

            # Save the updated conversation
            self.save_current_conversation()

            success = True
            total_time = time.time() - start_time

            # Track end-to-end performance
            self.metrics.track_end_to_end_performance(
                question=question,
                total_time=total_time,
                success=success,
                component_times=component_times
            )

            logger.info(f"Question processed successfully in {total_time:.3f}s")
            return response_content

        except ValueError as ve:
            # Handle validation errors
            error_msg = f"Validation error: {str(ve)}"
            logger.error(f"RAG Chain Error: {error_msg}")

            # Add error to chat history
            self.chat_history.append(AIMessage(content=error_msg))

            # Still save the conversation even with the error
            self.save_current_conversation()

            # Track failed request
            total_time = time.time() - start_time
            self.metrics.track_end_to_end_performance(
                question=question,
                total_time=total_time,
                success=False,
                component_times=component_times
            )

            return error_msg

        except Exception as e:
            error_msg = f"Error processing question: {str(e)}"
            logger.error(f"RAG Chain Error: {error_msg}")

            # Add error to chat history
            self.chat_history.append(AIMessage(content=error_msg))

            # Still save the conversation even with the error
            self.save_current_conversation()

            # Track failed request
            total_time = time.time() - start_time
            self.metrics.track_end_to_end_performance(
                question=question,
                total_time=total_time,
                success=False,
                component_times=component_times
            )

            return error_msg

    def clear_history(self):
        """Clear chat history."""
        self.chat_history = []

    def get_store_stats(self):
        """Get vector store statistics."""
        return self.retriever.get_store_stats()

    def get_performance_metrics(self, time_window_hours: int = 24):
        """Get comprehensive performance metrics for the specified time window."""
        return self.metrics.get_performance_summary(time_window_hours)

    def export_metrics(self, filepath: str) -> bool:
        """Export metrics data to file."""
        return self.metrics.export_metrics(filepath)

    # Conversation management methods
    def create_conversation(self, title: Optional[str] = None) -> str:
        """Create a new conversation and set it as active.

        Args:
            title: Optional title for the conversation

        Returns:
            str: Conversation ID
        """
        conv_id = self.conversation_store.create_conversation(title)
        self.active_conversation_id = conv_id
        self.chat_history = []
        return conv_id

    def load_conversation(self, conv_id: str) -> bool:
        """Load a conversation from storage and set it as active.

        Args:
            conv_id: Conversation ID to load

        Returns:
            bool: Success status
        """
        messages = self.conversation_store.load_messages(conv_id)
        if messages is not None:
            self.chat_history = messages
            self.active_conversation_id = conv_id
            return True
        return False

    def save_current_conversation(self) -> bool:
        """Save the current conversation to storage.

        Returns:
            bool: Success status
        """
        if not self.active_conversation_id:
            # Create a new conversation if none is active
            self.active_conversation_id = (
                self.conversation_store.create_conversation()
            )

        return self.conversation_store.save_messages(
            self.active_conversation_id, self.chat_history
        )

    def get_all_conversations(self) -> list[Conversation]:
        """Get all conversation metadata as Conversation models."""
        conversations = self.conversation_store.list_conversations()
        return [Conversation(**conv) for conv in conversations]

    def update_conversation_title(self, title: str) -> bool:
        """Update the title of the current conversation.

        Args:
            title: New title

        Returns:
            bool: Success status
        """
        if not self.active_conversation_id:
            return False

        return self.conversation_store.update_conversation_title(
            self.active_conversation_id, title
        )

    def delete_conversation(self, conv_id: str) -> bool:
        """Delete a conversation.

        Args:
            conv_id: Conversation ID

        Returns:
            bool: Success status
        """
        # If deleting the active conversation, clear history
        if conv_id == self.active_conversation_id:
            self.active_conversation_id = None
            self.chat_history = []

        return self.conversation_store.delete_conversation(conv_id)
