[project]
name = "terrabloom"
version = "0.1.0"
description = "RAG based SQL chatbot"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "black>=25.1.0",
    "coverage>=7.8.2",
    "faiss-cpu>=1.11.0",
    "langchain>=0.3.25",
    "langchain-community>=0.3.24",
    "langchain-core>=0.3.63",
    "langchain-google-genai>=2.1.5",
    "langchain-groq>=0.3.2",
    "langchain-openai>=0.3.24",
    "langsmith>=0.3.44",
    "numpy>=2.2.6",
    "pandas>=2.2.3",
    "psycopg2-binary>=2.9.10",
    "pytest>=8.4.0",
    "pytest-cov>=6.1.1",
    "pytest-mock>=3.14.1",
    "ruff>=0.12.1",
    "sqlalchemy>=2.0.41",
    "streamlit>=1.45.1",
    "typing-extensions>=4.14.0",
    "litestar>=2.6.0",
    "uvicorn>=0.35.0",
    "pre-commit>=4.2.0",
    "loguru>=0.7.3",
    "sentence-transformers>=5.0.0",
]

[dependency-groups]
dev = [
    "ruff>=0.11.8",
]

[tool.black]
line-length = 80

[tool.ruff.format]
indent-style = "space"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors (includes E501: line too long)
    "W",  # pycodestyle warnings
    "F",  # Pyflakes
    "I",  # isort
    "N",  # pep8-naming
    "D",  # pydocstyle
    # ... add more as needed
]
ignore = ["E501","D205","D415"]

[tool.ruff.lint.pydocstyle]
convention = "google"
