"""API application entry point module for the RAG system.

This module sets up the Litestar app, initializes core services via the ServiceContainer,
and provides dependency injection for controllers.
"""

from litestar import Litestar
from litestar.di import Provide
from litestar.exceptions import HTTPException

from src.api.controllers.ask import Ask<PERSON><PERSON>roller
from src.api.controllers.config import Config<PERSON><PERSON>roller
from src.api.controllers.conversation import ConversationController
from src.api.controllers.schema import SchemaController
from src.api.global_exception_handler import global_exception_handler
from src.api.service_container import service_container
from src.config.config import Config
from src.database.connection import DatabaseManager
from src.llm.chat_models import LLMManager
from src.vector_store.retriever import IVFFAISSRetriever


def initialize_app_services() -> None:
    """Initialize all core services in the ServiceContainer at app startup."""
    service_container.initialize()


def get_llm_manager() -> LLMManager:
    """Dependency provider for LLMManager from the ServiceContainer."""
    return service_container.get_llm_manager()


def get_db_manager() -> DatabaseManager:
    """Dependency provider for DatabaseManager from the ServiceContainer."""
    return service_container.get_db_manager()


def get_retriever() -> IVFFAISSRetriever:
    """Dependency provider for IVFFAISSRetriever from the ServiceContainer."""
    return service_container.get_retriever()


def get_config() -> Config:
    """Dependency provider for Config from the ServiceContainer."""
    return service_container.get_config()


app = Litestar(
    route_handlers=[
        AskController,
        ConversationController,
        SchemaController,
        ConfigController,
    ],
    dependencies={
        "llm_manager": Provide(get_llm_manager),
        "db_manager": Provide(get_db_manager),
        "retriever": Provide(get_retriever),
        "config": Provide(get_config),
    },
    on_startup=[initialize_app_services],
    debug=True,
    exception_handlers={
        Exception: global_exception_handler,
        HTTPException: global_exception_handler,
    },
)
