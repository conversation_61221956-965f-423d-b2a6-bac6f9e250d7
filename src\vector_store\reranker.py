"""Enhanced Reranker module for document ranking in RAG pipeline.

This module provides advanced reranking capabilities with multiple strategies,
query-aware scoring, and performance optimization for better schema retrieval.
"""

import re
from typing import Dict, List, Optional, Tuple

import numpy as np
from sentence_transformers import CrossEncoder
from loguru import logger

from src.config.config import config


class EnhancedReranker:
    """Enhanced reranker with multiple strategies and query-aware scoring.

    Attributes:
        model (CrossEncoder): The cross-encoder model used for scoring.
        fallback_model (CrossEncoder): Fallback model for robustness.
    """

    def __init__(self,
                 model_name: str = config.RERANKER_MODEL,
                 fallback_model: Optional[str] = None):
        """Initialize the Enhanced Reranker.

        Args:
            model_name (str): Name of the primary cross-encoder model.
            fallback_model (str, optional): Name of fallback model.
        """
        try:
            self.model = CrossEncoder(model_name)
            logger.info(f"Primary reranker model loaded: {model_name}")
        except Exception as e:
            logger.error(f"Failed to load primary reranker model {model_name}: {e}")
            # Use a more basic model as fallback
            self.model = CrossEncoder("cross-encoder/ms-marco-TinyBERT-L-2-v2")
            logger.info("Using fallback TinyBERT model")

        # Load fallback model if specified
        self.fallback_model = None
        if fallback_model:
            try:
                self.fallback_model = CrossEncoder(fallback_model)
                logger.info(f"Fallback reranker model loaded: {fallback_model}")
            except Exception as e:
                logger.warning(f"Failed to load fallback model {fallback_model}: {e}")

        # Query analysis patterns for enhanced scoring
        self.query_patterns = {
            'count_query': r'\b(count|how many|number of)\b',
            'specific_table': r'\b(table|from)\s+(\w+)',
            'column_specific': r'\b(column|field)\s+(\w+)',
            'join_query': r'\b(join|with|relate|connect)\b',
            'aggregation': r'\b(sum|avg|average|total|max|min)\b',
            'temporal': r'\b(date|time|when|recent|last|first)\b',
            'filter_query': r'\b(where|filter|condition|criteria)\b'
        }

    def predict_scores(self, query: str, documents: List[str], k: int = 10):
        """Predict relevance scores for a query and a list of documents.

        Args:
            query (str): The query string.
            documents (List[str]): List of document strings to score.
            k (int, optional): Number of top documents to consider. Defaults to 10.

        Returns:
            np.ndarray: Array of relevance scores for each document.
        """
        scores = self.model.predict([(query, passage) for passage in documents])
        return scores

    def rerank(
        self, query: str, documents: List[str], k: int = config.RERANKER_K
    ):
        """Rerank documents for a given query and return indices of top k documents.

        Args:
            query (str): The query string.
            documents (List[str]): List of document strings to rank.
            k (int, optional): Number of top documents to return. Defaults to config.RERANKER_K.

        Returns:
            np.ndarray: Indices of the top k ranked documents.
        """
        scores = self.predict_scores(query, documents)
        sorted_indices = np.argsort(scores)[::-1]
        return sorted_indices[:k]
